#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版书籍爬虫 - 使用requests + BeautifulSoup
适用于不需要JavaScript渲染的情况
"""

import requests
import time
import random
import re
import pandas as pd
from bs4 import BeautifulSoup
import logging
from typing import List, Dict
import json

class SimpleBookCrawler:
    """简化版书籍爬虫"""
    
    def __init__(self, min_delay: float = 2.0, max_delay: float = 8.0):
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.session = requests.Session()
        self.books_data = []
        
        # 配置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # 设置请求头，模拟真实浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
    
    def random_delay(self):
        """随机延迟"""
        delay = random.uniform(self.min_delay, self.max_delay)
        if random.random() < 0.1:  # 10%概率额外延迟
            delay += random.uniform(2, 8)
        
        self.logger.info(f"等待 {delay:.2f} 秒...")
        time.sleep(delay)
    
    def extract_word_count(self, book_info: str) -> str:
        """从书籍信息中提取字数"""
        word_pattern = r'(\d+)字'
        match = re.search(word_pattern, book_info)
        if match:
            return match.group(1) + '字'
        return "未知字数"
    
    def crawl_books(self, url: str) -> List[Dict]:
        """爬取书籍信息"""
        try:
            self.logger.info(f"开始访问: {url}")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找书籍元素
            book_names = soup.find_all('a', class_='MRecommendList_bookName__ctzJ3')
            book_authors = soup.find_all('a', class_='MRecommendList_bookAuthor__UKXyV')
            book_intros = soup.find_all('a', class_='MRecommendList_bookIntro__iEep2')
            
            self.logger.info(f"找到 {len(book_names)} 本书籍")
            
            min_length = min(len(book_names), len(book_authors), len(book_intros))
            
            for i in range(min_length):
                try:
                    book_name = book_names[i].get_text().strip()
                    book_info = book_authors[i].get_text().strip()
                    word_count = self.extract_word_count(book_info)
                    book_intro = book_intros[i].get_text().strip()
                    
                    book_data = {
                        '书名': book_name,
                        '字数': word_count,
                        '简介': book_intro
                    }
                    
                    self.books_data.append(book_data)
                    self.logger.info(f"成功爬取: {book_name}")
                    
                    # 随机延迟
                    if i < min_length - 1:  # 不是最后一个
                        self.random_delay()
                        
                except Exception as e:
                    self.logger.error(f"处理第 {i+1} 本书时出错: {e}")
                    continue
            
            return self.books_data
            
        except Exception as e:
            self.logger.error(f"爬取失败: {e}")
            return []
    
    def save_to_excel(self, filename: str = "books_simple.xlsx"):
        """保存到Excel"""
        if not self.books_data:
            self.logger.warning("没有数据可保存")
            return
        
        try:
            df = pd.DataFrame(self.books_data)
            df.to_excel(filename, index=False, engine='openpyxl')
            self.logger.info(f"数据已保存到 {filename}")
            print(f"\n数据预览:\n{df.head()}")
            print(f"\n总共保存了 {len(df)} 条记录")
        except Exception as e:
            self.logger.error(f"保存失败: {e}")


def main():
    """主函数"""
    target_url = "https://www.ishugui.com/browse/on3-tw282-th991-st2"
    
    crawler = SimpleBookCrawler(min_delay=2.0, max_delay=6.0)
    
    print("=== 简化版书籍爬虫启动 ===")
    
    try:
        books = crawler.crawl_books(target_url)
        
        if books:
            crawler.save_to_excel("书籍信息_简化版.xlsx")
            print(f"\n爬取成功！共获取 {len(books)} 本书籍信息")
        else:
            print("未能获取到数据")
            
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()
