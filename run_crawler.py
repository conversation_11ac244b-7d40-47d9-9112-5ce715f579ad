#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫启动脚本
提供简单的命令行界面
"""

import sys
import os

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = ['selenium', 'pandas', 'openpyxl', 'beautifulsoup4', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("📚 书籍爬虫工具")
    print("="*50)
    print("1. 启动隐蔽性爬虫 (推荐)")
    print("2. 启动简化版爬虫")
    print("3. 检查依赖包")
    print("4. 查看配置")
    print("5. 退出")
    print("="*50)

def show_config():
    """显示当前配置"""
    try:
        from config import TARGET_URL, MIN_DELAY, MAX_DELAY, MAX_BOOKS
        print("\n📋 当前配置:")
        print(f"目标网址: {TARGET_URL}")
        print(f"延迟范围: {MIN_DELAY}-{MAX_DELAY} 秒")
        print(f"最大爬取数量: {MAX_BOOKS}")
    except ImportError:
        print("\n📋 使用默认配置:")
        print("目标网址: https://www.ishugui.com/browse/on3-tw282-th991-st2")
        print("延迟范围: 3.0-10.0 秒")
        print("最大爬取数量: 30")
        print("\n💡 提示: 创建 config.py 文件可自定义配置")

def run_stealth_crawler():
    """运行隐蔽性爬虫"""
    print("\n🚀 启动隐蔽性爬虫...")
    try:
        from stealth_crawler import main
        main()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_simple_crawler():
    """运行简化版爬虫"""
    print("\n🚀 启动简化版爬虫...")
    try:
        from simple_crawler import main
        main()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def main():
    """主函数"""
    print("🔍 正在检查环境...")
    
    if not check_dependencies():
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                run_stealth_crawler()
            elif choice == '2':
                run_simple_crawler()
            elif choice == '3':
                check_dependencies()
            elif choice == '4':
                show_config()
            elif choice == '5':
                print("\n👋 再见!")
                break
            else:
                print("❌ 无效选择，请输入 1-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
