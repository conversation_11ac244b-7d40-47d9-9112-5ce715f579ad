#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高隐蔽性书籍信息爬虫工具
模拟真实用户浏览行为，具备反检测能力
"""

import time
import random
import re
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from typing import List, Dict, Optional
import json
import os

class StealthBookCrawler:
    """隐蔽性书籍爬虫类"""
    
    def __init__(self, min_delay: float = 2.0, max_delay: float = 8.0):
        """
        初始化爬虫
        
        Args:
            min_delay: 最小延迟时间（秒）
            max_delay: 最大延迟时间（秒）
        """
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.driver = None
        self.books_data = []
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('crawler.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 用户代理列表（模拟不同浏览器）
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
    
    def setup_driver(self) -> webdriver.Chrome:
        """设置Chrome驱动器，配置反检测选项"""
        chrome_options = Options()
        
        # 基础反检测配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 随机选择用户代理
        user_agent = random.choice(self.user_agents)
        chrome_options.add_argument(f'--user-agent={user_agent}')
        
        # 其他隐蔽性配置
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')  # 禁用图片加载以提高速度
        chrome_options.add_argument('--disable-javascript')  # 可选：禁用JS（如果网站不依赖JS）
        
        # 设置窗口大小（模拟真实用户）
        chrome_options.add_argument('--window-size=1366,768')
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            
            # 执行反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info(f"浏览器启动成功，用户代理: {user_agent}")
            return driver
            
        except Exception as e:
            self.logger.error(f"浏览器启动失败: {e}")
            raise
    
    def random_delay(self, extra_min: float = 0, extra_max: float = 0):
        """随机延迟，模拟人类行为"""
        min_time = self.min_delay + extra_min
        max_time = self.max_delay + extra_max
        delay = random.uniform(min_time, max_time)
        
        # 添加一些随机性：偶尔会有更长的停顿（模拟用户思考）
        if random.random() < 0.1:  # 10%的概率
            delay += random.uniform(3, 10)
        
        self.logger.info(f"等待 {delay:.2f} 秒...")
        time.sleep(delay)
    
    def simulate_human_behavior(self):
        """模拟人类浏览行为"""
        # 随机滚动页面
        if random.random() < 0.7:  # 70%概率滚动
            scroll_amount = random.randint(100, 500)
            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            time.sleep(random.uniform(0.5, 2.0))
        
        # 偶尔移动鼠标
        if random.random() < 0.3:  # 30%概率移动鼠标
            try:
                action = ActionChains(self.driver)
                x_offset = random.randint(-100, 100)
                y_offset = random.randint(-100, 100)
                action.move_by_offset(x_offset, y_offset).perform()
                time.sleep(random.uniform(0.2, 1.0))
            except:
                pass
    
    def extract_word_count(self, book_info: str) -> str:
        """从书籍信息中提取字数"""
        # 使用正则表达式匹配字数
        word_pattern = r'(\d+)字'
        match = re.search(word_pattern, book_info)
        if match:
            return match.group(1) + '字'
        return "未知字数"
    
    def crawl_books(self, url: str, max_books: int = 50) -> List[Dict]:
        """
        爬取书籍信息
        
        Args:
            url: 目标网址
            max_books: 最大爬取书籍数量
            
        Returns:
            书籍信息列表
        """
        self.driver = self.setup_driver()
        
        try:
            self.logger.info(f"开始访问: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "MRecommendList_bookName__ctzJ3"))
            )
            
            # 模拟人类行为
            self.simulate_human_behavior()
            self.random_delay(1, 3)
            
            # 查找所有书籍元素
            book_name_elements = self.driver.find_elements(By.CLASS_NAME, "MRecommendList_bookName__ctzJ3")
            book_author_elements = self.driver.find_elements(By.CLASS_NAME, "MRecommendList_bookAuthor__UKXyV")
            book_intro_elements = self.driver.find_elements(By.CLASS_NAME, "MRecommendList_bookIntro__iEep2")
            
            self.logger.info(f"找到 {len(book_name_elements)} 本书籍")
            
            # 确保三个列表长度一致
            min_length = min(len(book_name_elements), len(book_author_elements), len(book_intro_elements))
            min_length = min(min_length, max_books)
            
            for i in range(min_length):
                try:
                    # 提取书名
                    book_name = book_name_elements[i].text.strip()
                    
                    # 提取书籍信息并获取字数
                    book_info = book_author_elements[i].text.strip()
                    word_count = self.extract_word_count(book_info)
                    
                    # 提取简介
                    book_intro = book_intro_elements[i].text.strip()
                    
                    # 存储数据
                    book_data = {
                        '书名': book_name,
                        '字数': word_count,
                        '简介': book_intro
                    }
                    
                    self.books_data.append(book_data)
                    self.logger.info(f"成功爬取第 {i+1} 本书: {book_name}")
                    
                    # 每爬取几本书后模拟人类行为
                    if (i + 1) % 3 == 0:
                        self.simulate_human_behavior()
                        self.random_delay()
                    
                except Exception as e:
                    self.logger.error(f"爬取第 {i+1} 本书时出错: {e}")
                    continue
            
            self.logger.info(f"爬取完成，共获取 {len(self.books_data)} 本书籍信息")
            return self.books_data
            
        except TimeoutException:
            self.logger.error("页面加载超时")
            return []
        except Exception as e:
            self.logger.error(f"爬取过程中出错: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()
                self.logger.info("浏览器已关闭")
    
    def save_to_excel(self, filename: str = "books_data.xlsx"):
        """将数据保存到Excel文件"""
        if not self.books_data:
            self.logger.warning("没有数据可保存")
            return
        
        try:
            df = pd.DataFrame(self.books_data)
            df.to_excel(filename, index=False, engine='openpyxl')
            self.logger.info(f"数据已保存到 {filename}")
            
            # 显示数据预览
            print("\n数据预览:")
            print(df.head())
            print(f"\n总共保存了 {len(df)} 条记录")
            
        except Exception as e:
            self.logger.error(f"保存Excel文件时出错: {e}")
    
    def save_to_json(self, filename: str = "books_data.json"):
        """将数据保存到JSON文件（备份）"""
        if not self.books_data:
            return
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.books_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"数据已备份到 {filename}")
        except Exception as e:
            self.logger.error(f"保存JSON文件时出错: {e}")


def main():
    """主函数"""
    try:
        # 尝试导入配置文件
        from config import TARGET_URL, MIN_DELAY, MAX_DELAY, MAX_BOOKS, EXCEL_FILENAME, JSON_FILENAME
        target_url = TARGET_URL
        min_delay = MIN_DELAY
        max_delay = MAX_DELAY
        max_books = MAX_BOOKS
        excel_file = EXCEL_FILENAME
        json_file = JSON_FILENAME
    except ImportError:
        # 如果没有配置文件，使用默认值
        target_url = "https://www.ishugui.com/browse/on3-tw282-th991-st2"
        min_delay = 3.0
        max_delay = 10.0
        max_books = 30
        excel_file = "书籍信息.xlsx"
        json_file = "书籍信息_备份.json"

    # 创建爬虫实例
    crawler = StealthBookCrawler(min_delay=min_delay, max_delay=max_delay)

    print("=== 隐蔽性书籍爬虫启动 ===")
    print(f"目标网址: {target_url}")
    print(f"延迟范围: {min_delay}-{max_delay} 秒")
    print(f"最大爬取数量: {max_books}")
    print("正在启动浏览器...")

    try:
        # 开始爬取
        books = crawler.crawl_books(target_url, max_books=max_books)

        if books:
            # 保存到Excel
            crawler.save_to_excel(excel_file)

            # 保存JSON备份
            crawler.save_to_json(json_file)

            print(f"\n爬取成功！共获取 {len(books)} 本书籍信息")
            print(f"文件已保存为: {excel_file}")
        else:
            print("未能获取到任何数据，请检查网络连接或网站结构是否发生变化")

    except KeyboardInterrupt:
        print("\n用户中断爬取")
    except Exception as e:
        print(f"爬取过程中发生错误: {e}")


if __name__ == "__main__":
    main()
