#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫配置文件
可以在这里调整各种参数
"""

# 目标网址配置
TARGET_URL = "https://www.ishugui.com/browse/on3-tw282-th991-st2"

# 延迟时间配置（秒）
MIN_DELAY = 3.0  # 最小延迟
MAX_DELAY = 10.0  # 最大延迟

# 爬取数量限制
MAX_BOOKS = 50  # 最大爬取书籍数量

# 输出文件配置
EXCEL_FILENAME = "书籍信息.xlsx"
JSON_FILENAME = "书籍信息_备份.json"
LOG_FILENAME = "crawler.log"

# CSS选择器配置（如果网站结构变化，在这里修改）
SELECTORS = {
    'book_name': 'MRecommendList_bookName__ctzJ3',
    'book_author': 'MRecommendList_bookAuthor__UKXyV', 
    'book_intro': 'MRecommendList_bookIntro__iEep2'
}

# 浏览器配置
BROWSER_CONFIG = {
    'headless': False,  # 是否无头模式
    'window_size': '1366,768',  # 窗口大小
    'disable_images': True,  # 禁用图片加载
    'disable_javascript': False,  # 禁用JavaScript
}

# 用户代理列表
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
]

# 重试配置
RETRY_CONFIG = {
    'max_retries': 3,  # 最大重试次数
    'retry_delay': 5,  # 重试延迟（秒）
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',  # 日志级别：DEBUG, INFO, WARNING, ERROR
    'format': '%(asctime)s - %(levelname)s - %(message)s'
}
