# 隐蔽性书籍爬虫工具

这是一个专为爬取书籍信息设计的高隐蔽性爬虫工具，具备强大的反检测能力和人类行为模拟功能。

## 功能特点

### 🔒 高隐蔽性
- **用户代理轮换**：随机使用不同浏览器的User-Agent
- **反自动化检测**：禁用WebDriver检测特征
- **随机延迟**：非规律的访问间隔（2-10秒，可配置）
- **人类行为模拟**：随机滚动、鼠标移动等操作

### 📊 数据提取
- **书名**：准确提取书籍标题
- **字数**：从书籍信息中智能提取字数
- **简介**：完整的书籍描述内容

### 💾 数据导出
- **Excel格式**：主要输出格式，包含表头（书名、字数、简介）
- **JSON备份**：自动创建JSON格式备份文件
- **日志记录**：详细的运行日志

## 安装依赖

### 方法1：使用pip安装
```bash
pip install -r requirements.txt
```

### 方法2：手动安装
```bash
pip install selenium pandas openpyxl webdriver-manager beautifulsoup4 requests
```

### Chrome浏览器要求
- 需要安装Chrome浏览器
- 程序会自动下载对应版本的ChromeDriver

## 使用方法

### 主要爬虫（推荐）
```bash
python stealth_crawler.py
```

### 简化版爬虫
```bash
python simple_crawler.py
```

## 文件说明

### stealth_crawler.py
- **主要爬虫工具**
- 使用Selenium WebDriver
- 具备完整的反检测功能
- 适用于JavaScript渲染的网站

### simple_crawler.py
- **轻量级版本**
- 使用requests + BeautifulSoup
- 速度更快，资源占用少
- 适用于静态HTML网站

### 配置参数

可以在代码中调整以下参数：

```python
# 延迟时间配置
crawler = StealthBookCrawler(
    min_delay=3.0,    # 最小延迟（秒）
    max_delay=10.0    # 最大延迟（秒）
)

# 最大爬取数量
books = crawler.crawl_books(url, max_books=30)
```

## 输出文件

运行成功后会生成以下文件：

- `书籍信息.xlsx` - 主要数据文件
- `书籍信息_备份.json` - JSON格式备份
- `crawler.log` - 运行日志

## 安全提示

### ⚠️ 使用注意事项
1. **遵守robots.txt**：请检查目标网站的robots.txt文件
2. **合理频率**：不要设置过高的爬取频率
3. **法律合规**：确保爬取行为符合当地法律法规
4. **网站条款**：遵守目标网站的使用条款

### 🛡️ 反检测措施
- 随机User-Agent轮换
- 禁用自动化检测标识
- 模拟真实用户行为
- 非规律访问间隔
- 随机鼠标移动和页面滚动

## 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   ```
   解决：程序会自动下载匹配的ChromeDriver
   ```

2. **网络连接超时**
   ```
   解决：检查网络连接，或增加超时时间
   ```

3. **元素定位失败**
   ```
   解决：网站结构可能发生变化，需要更新CSS选择器
   ```

4. **反爬虫检测**
   ```
   解决：增加延迟时间，或使用代理IP
   ```

## 自定义配置

### 修改目标网站
```python
target_url = "你的目标网址"
```

### 调整CSS选择器
如果网站结构发生变化，需要更新以下选择器：
```python
# 书名选择器
book_name_elements = driver.find_elements(By.CLASS_NAME, "MRecommendList_bookName__ctzJ3")

# 作者信息选择器  
book_author_elements = driver.find_elements(By.CLASS_NAME, "MRecommendList_bookAuthor__UKXyV")

# 简介选择器
book_intro_elements = driver.find_elements(By.CLASS_NAME, "MRecommendList_bookIntro__iEep2")
```

## 技术支持

如果遇到问题，请检查：
1. Python版本（建议3.8+）
2. 依赖包版本
3. Chrome浏览器版本
4. 网络连接状态

## 免责声明

本工具仅供学习和研究使用。使用者应当：
- 遵守相关法律法规
- 尊重网站版权和使用条款  
- 合理控制爬取频率
- 承担使用风险

作者不对使用本工具产生的任何后果承担责任。
