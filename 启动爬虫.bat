@echo off
chcp 65001 >nul
title 书籍爬虫工具

echo.
echo ========================================
echo           书籍爬虫工具启动器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测通过

REM 检查pip是否可用
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip不可用，请检查Python安装
    pause
    exit /b 1
)

echo ✅ pip工具检测通过

REM 检查是否存在requirements.txt
if not exist "requirements.txt" (
    echo ❌ 未找到requirements.txt文件
    pause
    exit /b 1
)

echo.
echo 🔍 正在检查依赖包...
pip install -r requirements.txt --quiet

if errorlevel 1 (
    echo ❌ 依赖包安装失败，请检查网络连接
    pause
    exit /b 1
)

echo ✅ 依赖包检查完成

echo.
echo 🚀 启动爬虫工具...
python run_crawler.py

echo.
echo 按任意键退出...
pause >nul
